'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FaSearch } from 'react-icons/fa';
import SearchModal from './SearchModal';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);

  return (
    <header className="bg-background border-b border-secondary-dark">
      <div className="container-custom py-4">
        <nav className="flex justify-between items-center">
          <div className="flex items-center">
            <Link href="/" className="flex items-center">
              <Image
                src="/icons/no-bg-logo.png"
                alt="Umugore Uzashimwa"
                width={120}
                height={40}
                className="h-10 w-auto"
                priority
              />
            </Link>
          </div>
          
          {/* Desktop Navigation */}
          <div className="hidden md:flex space-x-6">
            <Link href="/" className="font-medium">Ahabanza</Link>
            <Link href="/articles" className="font-medium">In<PERSON><PERSON><PERSON> ngufi</Link>
            <Link href="/testimonies" className="font-medium">Ubuhamya</Link>
            <Link href="/partners" className="font-medium">Abafatanya bikorwa</Link>
            <Link href="/about" className="font-medium">Aboturibo</Link>
          </div>
          
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setIsSearchOpen(true)}
              className="p-2 text-primary hover:text-primary-dark transition-colors touch-manipulation"
              aria-label="Search"
            >
              <FaSearch className="w-5 h-5" />
            </button>
            <Link href="/subscribe" className="btn btn-primary hidden md:block">Iyandikishe</Link>

            {/* Mobile Menu Button */}
            <button
              className="md:hidden p-2 text-primary hover:text-primary-dark transition-colors touch-manipulation"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              aria-label="Toggle menu"
            >
              {isMenuOpen ? (
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="w-6 h-6">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="w-6 h-6">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              )}
            </button>
          </div>
        </nav>
        
        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-secondary-dark mt-4">
            <div className="flex flex-col space-y-4">
              <Link href="/" className="font-medium" onClick={() => setIsMenuOpen(false)}>Ahabanza</Link>
              <Link href="/articles" className="font-medium" onClick={() => setIsMenuOpen(false)}>Inyandiko ngufi</Link>
              <Link href="/testimonies" className="font-medium" onClick={() => setIsMenuOpen(false)}>Ubuhamya</Link>
              <Link href="/partners" className="font-medium" onClick={() => setIsMenuOpen(false)}>Abafatanya bikorwa</Link>
              <Link href="/about" className="font-medium" onClick={() => setIsMenuOpen(false)}>Aboturibo</Link>
              <Link href="/subscribe" className="btn btn-primary inline-block text-center" onClick={() => setIsMenuOpen(false)}>Iyandikishe</Link>
            </div>
          </div>
        )}
      </div>

      {/* Search Modal */}
      <SearchModal
        isOpen={isSearchOpen}
        onClose={() => setIsSearchOpen(false)}
      />
    </header>
  );
}
