'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { api } from '../lib/api';

export default function Footer() {
  const [categories, setCategories] = useState([]);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await api.categories.getAll({ pageSize: 6 });
        setCategories(response.results || []);
      } catch (error) {
        console.error('Error fetching categories for footer:', error);
        // Keep categories empty to show loading skeleton
        setCategories([]);
      }
    };

    fetchCategories();
  }, []);
  return (
    <footer className="bg-neutral-800 text-neutral-300 py-12">
      <div className="container-custom">
        <div className="grid md:grid-cols-4 gap-8">
          <div>
            <h3 className="text-xl font-bold text-white mb-4"><PERSON><PERSON><PERSON></h3>
            <p className="mb-4"><PERSON><PERSON><PERSON> wuba<PERSON> ni<PERSON>.</p>
          </div>
          <div>
            <h4 className="text-lg font-bold text-white mb-4">Urutonde rw'ihuse</h4>
            <ul className="space-y-2">
              <li><Link href="/" className="hover:text-white">Ahabanza</Link></li>
              <li><Link href="/articles" className="hover:text-white">Inyandiko ngufi</Link></li>
              <li><Link href="/testimonies" className="hover:text-white">Ubuhamya</Link></li>
              <li><Link href="/partners" className="hover:text-white">Abafatanyabikorwa</Link></li>
            </ul>
          </div>
          <div>
            <h4 className="text-lg font-bold text-white mb-4">Inyandiko mu ibyiciro</h4>
            <ul className="space-y-2">
              {categories.length > 0 ? (
                categories.slice(0, 4).map((category) => (
                  <li key={category.slug}>
                    <Link href={`/categories/${category.slug}`} className="hover:text-white">
                      {category.name}
                    </Link>
                  </li>
                ))
              ) : (
                // Loading skeleton for categories
                <>
                  <li><div className="h-4 bg-primary-light rounded w-24 animate-pulse"></div></li>
                  <li><div className="h-4 bg-primary-light rounded w-32 animate-pulse"></div></li>
                  <li><div className="h-4 bg-primary-light rounded w-20 animate-pulse"></div></li>
                  <li><div className="h-4 bg-primary-light rounded w-28 animate-pulse"></div></li>
                </>
              )}
              {categories.length > 4 && (
                <li>
                  <Link href="/categories" className="hover:text-white text-sm text-neutral-400">
                    Reba ibyiciro byose →
                  </Link>
                </li>
              )}
            </ul>
          </div>
          <div>
            <h4 className="text-lg font-bold text-white mb-4">Menya byinshi</h4>
            <ul className="space-y-2">
              <li><Link href="/about" className="hover:text-white">Aboturibo</Link></li>
              <li><Link href="/contact" className="hover:text-white">Tuvugishe</Link></li>
              <li><Link href="/privacy" className="hover:text-white">Ubwirinzi</Link></li>
              <li><Link href="/terms" className="hover:text-white">Amabwiriza</Link></li>
            </ul>
          </div>
        </div>
        <div className="border-t border-neutral-700 mt-8 pt-8 text-center">
          <p>&copy; {new Date().getFullYear()} Umugore Uzashimwa. Uburenganzira bwose burabitswe.</p>
        </div>
      </div>
    </footer>
  );
}
