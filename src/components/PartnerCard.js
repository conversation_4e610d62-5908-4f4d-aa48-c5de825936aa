import Link from 'next/link';
import { FaExternalLinkAlt } from 'react-icons/fa';

export default function PartnerCard({ partner }) {
  const {
    id,
    name,
    slug,
    description,
    website,
    categories = [],
    status,
    logo,
  } = partner;

  return (
    <div className="bg-background rounded-lg overflow-hidden border border-secondary-dark hover:border-primary-light transition-colors">
      {/* Logo/Image section */}
      <div className="h-32 bg-secondary flex items-center justify-center p-4">
        {logo ? (
          <img 
            src={logo} 
            alt={`${name} logo`}
            className="max-h-full max-w-full object-contain"
          />
        ) : (
          <div className="text-primary text-center">
            <div className="text-2xl font-bold">{name.charAt(0)}</div>
          </div>
        )}
      </div>

      <div className="p-6">
        {/* Categories */}
        {categories.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-3">
            {categories.map((category) => (
              <span
                key={category.slug}
                className="text-xs bg-primary-light text-primary-dark px-2 py-1 rounded-full"
              >
                {category.name}
              </span>
            ))}
          </div>
        )}

        {/* Partner name */}
        <Link href={`/partners/${slug}`}>
          <h3 className="text-xl font-semibold mb-2 hover:text-primary transition-colors">
            {name}
          </h3>
        </Link>

        {/* Description */}
        {description && (
          <p className="text-primary-dark mb-4 line-clamp-3">
            {description}
          </p>
        )}

        {/* Actions */}
        <div className="flex justify-between items-center">
          <Link
            href={`/partners/${slug}`}
            className="text-primary font-medium hover:text-primary-dark"
          >
            Menya byinshi
          </Link>

          {website && (
            <a
              href={website}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-1 text-sm text-primary-dark hover:text-primary"
            >
              Sura urubuga
              <FaExternalLinkAlt className="text-xs" />
            </a>
          )}
        </div>
      </div>
    </div>
  );
}
