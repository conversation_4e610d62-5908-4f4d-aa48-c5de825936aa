'use client';

import { useState } from 'react';
import { FaEnvelope } from 'react-icons/fa';

export default function NewsletterForm() {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState('');
  const [isSuccess, setIsSuccess] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!email) {
      setMessage('Nyabuna wandike aderesi yawe ya email');
      return;
    }
    
    setIsSubmitting(true);
    setMessage('');
    
    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      setIsSuccess(true);
      setMessage('Murakoze kwiyandikisha kuri newsletter yacu!');
      setEmail('');
      
      // Reset success message after 5 seconds
      setTimeout(() => {
        setMessage('');
        setIsSuccess(false);
      }, 5000);
    }, 1500);
  };

  return (
    <div>
      <form onSubmit={handleSubmit} className="flex flex-col md:flex-row gap-4 max-w-xl mx-auto">
        <input
          type="email"
          placeholder="Aderesi yawe ya email"
          className="flex-grow px-4 py-3 rounded-md text-primary bg-background focus:outline-none focus:ring-2 focus:ring-accent border border-secondary-dark"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          disabled={isSubmitting}
        />
        <button
          type="submit"
          className="btn bg-accent text-white hover:bg-accent-dark flex items-center justify-center gap-2"
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Iyandikisha...' : (
            <>
              Iyandikishe <FaEnvelope />
            </>
          )}
        </button>
      </form>
      
      {message && (
        <div className={`mt-4 text-center ${isSuccess ? 'text-success' : 'text-accent'}`}>
          {message}
        </div>
      )}
    </div>
  );
}
