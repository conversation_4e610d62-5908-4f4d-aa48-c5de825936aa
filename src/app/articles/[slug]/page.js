'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import { FaCalendarAlt, FaTag } from 'react-icons/fa';
import Header from '../../../components/Header';
import Footer from '../../../components/Footer';
import ShareButtons from '../../../components/ShareButtons';
import LoadingSpinner from '../../../components/LoadingSpinner';
import ErrorMessage from '../../../components/ErrorMessage';
import { api } from '../../../lib/api';
import { stripHtml } from '../../../lib/htmlUtils';

export default function ArticlePage() {
  const params = useParams();
  const [article, setArticle] = useState(null);
  const [relatedArticles, setRelatedArticles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchArticleData = async () => {
      if (!params.slug) return;

      setLoading(true);
      setError(null);

      try {
        // Fetch article
        const articleData = await api.articles.getBySlug(params.slug);
        setArticle(articleData);

        // Fetch related articles from the same category
        if (articleData.categories && articleData.categories.length > 0) {
          const categoryName = articleData.categories[0].name;
          try {
            const relatedResponse = await api.articles.getAll({
              categoryName: categoryName,
              pageSize: 4
            });
            const filtered = relatedResponse.results?.filter(a => a.slug !== params.slug).slice(0, 3) || [];
            setRelatedArticles(filtered);
          } catch (relatedError) {
            console.error('Error fetching related articles:', relatedError);
            // Fallback to general articles
            const fallbackResponse = await api.articles.getAll({ pageSize: 4 });
            const filtered = fallbackResponse.results?.filter(a => a.slug !== params.slug).slice(0, 3) || [];
            setRelatedArticles(filtered);
          }
        } else {
          // Fallback to general articles if no categories
          const fallbackResponse = await api.articles.getAll({ pageSize: 4 });
          const filtered = fallbackResponse.results?.filter(a => a.slug !== params.slug).slice(0, 3) || [];
          setRelatedArticles(filtered);
        }
      } catch (err) {
        console.error('Error fetching article:', err);
        setError(err.message || 'Failed to load article');
      } finally {
        setLoading(false);
      }
    };

    fetchArticleData();
  }, [params.slug]);

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <div className="flex-1 flex items-center justify-center">
          <LoadingSpinner />
        </div>
        <Footer />
      </div>
    );
  }

  if (error || !article) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <div className="flex-1 flex items-center justify-center">
          <ErrorMessage message={error || 'Article not found'} />
        </div>
        <Footer />
      </div>
    );
  }

  // Format date
  const displayDate = article.created_at ? new Date(article.created_at).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }) : '';

  // Get primary category and author
  const primaryCategory = article.categories?.[0] || { name: 'Article', slug: 'article' };
  const primaryAuthor = article.authors?.[0] || { name: 'Anonymous', image: null };
  const articleImage = article.featured_image || article.image;

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      {/* Article Header */}
      <section className="pt-12 pb-8 bg-primary text-white">
        <div className="container-custom">
          <Link href="/articles" className="inline-block mb-4 hover:underline">
            ← Subira ku nyandiko
          </Link>
          <h1 className="mb-4">{article.title}</h1>
          <p className="text-lg mb-6 max-w-3xl">
            {article.short_description ?
              stripHtml(article.short_description) :
              (article.content ? stripHtml(article.content, 200) : '')
            }
          </p>
          <div className="flex flex-wrap items-center gap-6">
            <div className="flex items-center">
              <div className="w-12 h-12 rounded-full bg-neutral-300 mr-3 relative overflow-hidden">
                {primaryAuthor.image ? (
                  <img
                    src={primaryAuthor.image}
                    alt={primaryAuthor.name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="absolute inset-0 flex items-center justify-center text-neutral-500 font-semibold">
                    {primaryAuthor.name.charAt(0)}
                  </div>
                )}
              </div>
              <div>
                <div className="font-medium">{primaryAuthor.name}</div>
              </div>
            </div>
            <div className="flex items-center">
              <FaCalendarAlt className="mr-2" />
              <span>{displayDate}</span>
            </div>
            <div className="flex items-center">
              <FaTag className="mr-2" />
              <Link href={`/categories/${primaryCategory.slug}`} className="hover:underline">
                {primaryCategory.name}
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Article Content */}
      <section className="py-12 bg-white">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-12">
            {/* Main Content */}
            <div className="lg:col-span-8">
              {articleImage && (
                <div className="h-80 md:h-96 mb-8 rounded-lg relative overflow-hidden">
                  <img
                    src={articleImage}
                    alt={article.title}
                    className="w-full h-full object-cover"
                  />
                </div>
              )}

              <div className="prose prose-lg max-w-none" dangerouslySetInnerHTML={{ __html: article.content }}></div>

              {/* Categories as tags */}
              {article.categories && article.categories.length > 0 && (
                <div className="mt-8 pt-8 border-t">
                  <h4 className="text-sm font-medium text-neutral-600 mb-3">Ibyiciro:</h4>
                  <div className="flex flex-wrap gap-2">
                    {article.categories.map((category) => (
                      <Link
                        key={category.slug}
                        href={`/categories/${category.slug}`}
                        className="px-3 py-1 bg-neutral-100 text-neutral-700 rounded-full hover:bg-neutral-200 text-sm"
                      >
                        {category.name}
                      </Link>
                    ))}
                  </div>
                </div>
              )}

              {/* Share */}
              <div className="mt-8 pt-8 border-t">
                <ShareButtons
                  item={article}
                  type="article"
                  title="Sangiza Inyandiko"
                />
              </div>

              {/* Author Bio */}
              {primaryAuthor && (
                <div className="mt-8 pt-8 border-t bg-neutral-50 p-6 rounded-lg">
                  <h3 className="text-xl mb-4">Ku mwanditsi</h3>
                  <div className="flex flex-col sm:flex-row gap-4">
                    <div className="w-20 h-20 rounded-full bg-neutral-300 relative overflow-hidden flex-shrink-0">
                      {primaryAuthor.image ? (
                        <img
                          src={primaryAuthor.image}
                          alt={primaryAuthor.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="absolute inset-0 flex items-center justify-center text-neutral-500 text-xl font-semibold">
                          {primaryAuthor.name.charAt(0)}
                        </div>
                      )}
                    </div>
                    <div>
                      <h4 className="text-lg font-medium">{primaryAuthor.name}</h4>
                      {primaryAuthor.bio && <p>{primaryAuthor.bio}</p>}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-4">
              {relatedArticles.length > 0 && (
                <div className="bg-neutral-50 p-6 rounded-lg mb-8">
                  <h3 className="text-xl mb-4">Inyandiko zijyanye</h3>
                  <div className="space-y-6">
                    {relatedArticles.map((relatedArticle) => (
                      <div key={relatedArticle.id} className="flex gap-4">
                        <div className="w-20 h-20 bg-neutral-200 rounded flex-shrink-0 relative overflow-hidden">
                          {(relatedArticle.featured_image || relatedArticle.image) ? (
                            <img
                              src={relatedArticle.featured_image || relatedArticle.image}
                              alt={relatedArticle.title}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="absolute inset-0 flex items-center justify-center text-neutral-500 text-xs">
                              Image
                            </div>
                          )}
                        </div>
                        <div>
                          <Link href={`/articles/${relatedArticle.slug}`} className="font-medium hover:text-primary">
                            {relatedArticle.title}
                          </Link>
                          <div className="text-sm text-neutral-500 mt-1">
                            {relatedArticle.created_at ? new Date(relatedArticle.created_at).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric'
                            }) : ''}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="bg-primary text-white p-6 rounded-lg">
                <h3 className="text-xl mb-4">Iyandikishe ku butumwa bwacu</h3>
                <p className="mb-4">Habwa inyandiko nshya n'amashusho agezweho kuri email yawe.</p>
                <form>
                  <input
                    type="email"
                    placeholder="Aderesi yawe ya email"
                    className="w-full px-4 py-2 rounded-md text-neutral-900 mb-3"
                  />
                  <button type="submit" className="w-full btn bg-accent text-neutral-800 hover:bg-accent-dark">
                    Iyandikishe
                  </button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
