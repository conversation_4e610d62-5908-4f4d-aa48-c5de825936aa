'use client';

import { useState, useEffect } from 'react';
import Header from '../../components/Header';
import Footer from '../../components/Footer';
import LoadingSpinner from '../../components/LoadingSpinner';
import ErrorMessage from '../../components/ErrorMessage';
import { CategoryCardSkeleton } from '../../components/SkeletonCard';
import { api } from '../../lib/api';
import Link from 'next/link';
import { FaTag, FaArrowRight } from 'react-icons/fa';

export default function CategoriesPage() {
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchCategories = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await api.categories.getAll({ pageSize: 100 });
      setCategories(response.results || []);
    } catch (err) {
      setError(err.message);
      setCategories([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      {/* Page Header */}
      <section className="bg-primary py-12 text-white">
        <div className="container-custom">
          <h1 className="mb-4">Categories</h1>
          <p className="text-lg max-w-3xl">
            Explore articles by category to find content that interests you most.
          </p>
        </div>
      </section>

      {/* Categories Grid */}
      <section className="section bg-neutral-100">
        <div className="container-custom">
          {loading ? (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {Array.from({ length: 6 }, (_, i) => (
                <CategoryCardSkeleton key={i} />
              ))}
            </div>
          ) : error ? (
            <ErrorMessage
              message={error}
              onRetry={fetchCategories}
              className="max-w-md mx-auto"
            />
          ) : categories.length > 0 ? (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {categories.map((category) => (
                <div key={category.id} className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow">
                  <div className="p-6">
                    <div className="flex items-center mb-4">
                      <div className="w-12 h-12 bg-primary-light rounded-lg flex items-center justify-center mr-4">
                        <FaTag className="text-primary text-xl" />
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold">{category.name}</h3>
                      </div>
                    </div>

                    {category.description && (
                      <p className="text-neutral-600 mb-4 line-clamp-3">
                        {category.description}
                      </p>
                    )}

                    <div className="flex justify-between items-center">
                      <Link
                        href={`/categories/${category.slug}`}
                        className="flex items-center gap-2 text-primary font-medium hover:text-primary-dark"
                      >
                        View Articles
                        <FaArrowRight className="text-sm" />
                      </Link>

                      <div className="text-sm text-neutral-500">
                        {category.created_at ? new Date(category.created_at).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'short'
                        }) : ''}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <h3 className="text-xl font-semibold text-neutral-600 mb-2">No categories found</h3>
              <p className="text-neutral-500">Check back later for new categories.</p>
            </div>
          )}
        </div>
      </section>

      <Footer />
    </div>
  );
}
